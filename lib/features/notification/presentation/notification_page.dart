import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/widgets/appbar/app_bar_common.dart';
import 'package:mcdc/shared/presentation/widgets/dialog/dialog_confirm.dart';
import 'package:mcdc/shared/presentation/widgets/dialog/dialog_success.dart';
import 'package:mcdc/features/news/presentation/news_detail_page.dart';
import 'package:mcdc/features/news/domain/entities/news.dart';
import 'package:mcdc/features/main/presentation/utils/main_page_navigation.dart';

import '../domain/entities/notification.dart' as notification_entity;
import 'components/notification_item.dart';

@RoutePage()
class NotificationPage extends StatefulWidget {
  const NotificationPage({super.key});

  @override
  State<NotificationPage> createState() => _NotificationPageState();
}

class _NotificationPageState extends State<NotificationPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _currentTabIndex = 0;

  // Selection mode state
  bool _isSelectionMode = false;
  Set<String> _selectedNotificationIds = {};

  // Tab categories
  final List<String> _tabTitles = [
    'ทั้งหมด',
    'บริการ',
    'ทั่วไป',
    'ระบบจับคู่',
    'โครงการ',
    'ข่าวประกาศ',
  ];

  // Mock notification data
  late List<notification_entity.Notification> _allNotifications;
  late Map<String, List<notification_entity.Notification>>
  _groupedNotifications;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabTitles.length, vsync: this);
    _tabController.addListener(() {
      if (_tabController.index != _currentTabIndex) {
        setState(() {
          _currentTabIndex = _tabController.index;
        });
      }
    });
    _initializeMockData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _initializeMockData() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final lastWeek = today.subtract(const Duration(days: 7));

    _allNotifications = [
      notification_entity.Notification(
        id: '1',
        title: 'แจ้งผลการพิจารณา',
        category: 'บริการ',
        time: '10:09',
        date: today,
      ),
      notification_entity.Notification(
        id: '2',
        title: 'แจ้งผลการพิจารณา',
        category: 'บริการ',
        time: '10:09',
        date: yesterday,
      ),
      notification_entity.Notification(
        id: '3',
        title:
            'รายงานผลการให้บริการของศูนย์ข้อมูลที่ปรึกษา ณ วันที่ 30 มิถุนายน 2566 (ไตรมาสที่ 3)',
        category: 'ทั่วไป',
        time: '13:01',
        date: yesterday,
      ),
      notification_entity.Notification(
        id: '4',
        title:
            'รายงานผลการให้บริการของศูนย์ข้อมูลที่ปรึกษา ณ วันที่ 30 มิถุนายน 2566 (ไตรมาสที่ 3)',
        category: 'ข่าวประกาศ',
        time: '13:01',
        date: lastWeek,
      ),
      notification_entity.Notification(
        id: '5',
        title:
            'รายงานผลการให้บริการของศูนย์ข้อมูลที่ปรึกษา ณ วันที่ 30 มิถุนายน 2566 (ไตรมาสที่ 3)',
        category: 'ระบบจับคู่',
        time: '13:01',
        date: lastWeek,
      ),
      notification_entity.Notification(
        id: '6',
        title:
            'รายงานผลการให้บริการของศูนย์ข้อมูลที่ปรึกษา ณ วันที่ 30 มิถุนายน 2566 (ไตรมาสที่ 3)',
        category: 'โครงการ',
        time: '13:01',
        date: lastWeek,
      ),
    ];

    _groupNotificationsByDate();
  }

  void _groupNotificationsByDate() {
    _groupedNotifications = {};

    for (final notification in _getFilteredNotifications()) {
      final dateKey = _formatDateKey(notification.date);
      if (_groupedNotifications[dateKey] == null) {
        _groupedNotifications[dateKey] = [];
      }
      _groupedNotifications[dateKey]!.add(notification);
    }
  }

  List<notification_entity.Notification> _getFilteredNotifications() {
    if (_currentTabIndex == 0) {
      return _allNotifications; // ทั้งหมด
    }

    final selectedCategory = _tabTitles[_currentTabIndex];
    return _allNotifications
        .where((notification) => notification.category == selectedCategory)
        .toList();
  }

  String _formatDateKey(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final normalizedDate = DateTime(date.year, date.month, date.day);

    if (normalizedDate.isAtSameMomentAs(today)) {
      return 'วันนี้';
    } else if (normalizedDate.isAtSameMomentAs(yesterday)) {
      return 'เมื่อวาน';
    } else {
      // Use simple date formatting to avoid locale issues
      return '${date.day} ${_getThaiMonth(date.month)} ${date.year + 543}';
    }
  }

  String _getThaiMonth(int month) {
    const thaiMonths = [
      'ม.ค.',
      'ก.พ.',
      'มี.ค.',
      'เม.ย.',
      'พ.ค.',
      'มิ.ย.',
      'ก.ค.',
      'ส.ค.',
      'ก.ย.',
      'ต.ค.',
      'พ.ย.',
      'ธ.ค.',
    ];

    // Safety check to prevent index out of range
    if (month < 1 || month > 12) {
      return 'ม.ค.'; // Default to January if invalid month
    }

    return thaiMonths[month - 1];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.surfaceDefault,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              physics: const NeverScrollableScrollPhysics(),
              children: List.generate(
                _tabTitles.length,
                (index) => _buildNotificationList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    if (_isSelectionMode) {
      return AppBar(
        backgroundColor: AppColors.backgroundDefault,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: AppColors.textDefaultDark),
          onPressed: _exitSelectionMode,
        ),
        title: Text(
          '${_selectedNotificationIds.length} รายการ',
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 20.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textDefaultDark,
          ),
        ),
        actions: [
          if (_selectedNotificationIds.isNotEmpty)
            TextButton(
              onPressed: _showDeleteConfirmation,
              child: Text(
                'ลบ',
                style: TextStyle(
                  fontFamily: AppFonts.notoSansThai,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w400,
                  color: AppColors.textCritical,
                ),
              ),
            ),
        ],
      );
    }

    return const AppBarCommon(title: 'แจ้งเตือน');
  }

  Widget _buildTabBar() {
    return Container(
      color: AppColors.surfaceDefault,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
      child: SizedBox(
        height: 48.h,
        child: ListView.separated(
          scrollDirection: Axis.horizontal,
          physics: const BouncingScrollPhysics(),
          itemCount: _tabTitles.length,
          separatorBuilder: (context, index) => 24.w.horizontalSpace,
          itemBuilder: (context, index) {
            final isSelected = _currentTabIndex == index;

            return GestureDetector(
              onTap: () {
                setState(() {
                  _currentTabIndex = index;
                });
                _tabController.animateTo(index);
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                decoration: BoxDecoration(
                  color:
                      isSelected
                          ? AppColors.surfacePrimarySubdude
                          : Colors.transparent,
                  borderRadius: BorderRadius.circular(50.r),
                ),
                child: Center(
                  child: Text(
                    _tabTitles[index],
                    style: TextStyle(
                      fontFamily: AppFonts.notoSansThai,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      height: 1.5,
                      color:
                          isSelected
                              ? AppColors.textPrimary
                              : AppColors.textSubdude,
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildNotificationList() {
    // Rebuild grouped notifications when tab changes
    _groupNotificationsByDate();

    if (_groupedNotifications.isEmpty) {
      return _buildEmptyState();
    }

    final dateKeys = _groupedNotifications.keys.toList();

    return ListView.builder(
      physics: const BouncingScrollPhysics(),
      itemCount: dateKeys.length,
      itemBuilder: (context, index) {
        if (index >= dateKeys.length) {
          return const SizedBox.shrink();
        }

        final dateKey = dateKeys[index];
        final notifications = _groupedNotifications[dateKey];

        if (notifications == null || notifications.isEmpty) {
          return const SizedBox.shrink();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDateHeader(dateKey),
            ...notifications.map(
              (notification) => NotificationItem(
                notification: notification,
                onTap: () => _handleNotificationTap(notification),
                onLongPress: () => _enterSelectionMode(notification),
                isSelectionMode: _isSelectionMode,
                isSelected: _selectedNotificationIds.contains(notification.id),
                onSelectionChanged:
                    (isSelected) =>
                        _handleSelectionChanged(notification.id, isSelected),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDateHeader(String dateText) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Text(
        dateText,
        style: TextStyle(
          fontFamily: AppFonts.notoSansThai,
          fontSize: 16.sp,
          fontWeight: FontWeight.w600,
          color: AppColors.textDefaultDark,
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Text(
          'ไม่มีการแจ้งเตือน',
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 16.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textSubdude,
            height: 1.5,
          ),
        ),
      ),
    );
  }

  void _handleNotificationTap(notification_entity.Notification notification) {
    // Navigate to news detail page if it's a news notification
    if (notification.category == 'ข่าวประกาศ') {
      _navigateToNewsDetail(notification);
    } else {
      // Handle other notification types with dynamic routing examples
      _handleSpecialNotificationTypes(notification);
    }
  }

  /// Demonstrates dynamic routing based on notification content
  void _handleSpecialNotificationTypes(
    notification_entity.Notification notification,
  ) {
    // Example: Navigate to specific tabs based on notification category
    switch (notification.category) {
      case 'โครงการใหม่':
      case 'การจับคู่':
        // Navigate to matching tab for project-related notifications
        MainPageNavigation.navigateToMatching(context, replace: true);
        break;
      case 'สถานะโครงการ':
      case 'การติดตาม':
        // Navigate to tracking tab for status updates
        MainPageNavigation.navigateToTracking(context, replace: true);
        break;
      case 'ข้อมูลโปรไฟล์':
      case 'การตั้งค่า':
        // Navigate to profile tab for profile-related notifications
        MainPageNavigation.navigateToProfile(context, replace: true);
        break;
      case 'ผลการค้นหา':
        // Navigate to search tab for search-related notifications
        MainPageNavigation.navigateToSearch(context, replace: true);
        break;
      default:
        // Default to dashboard for general notifications
        MainPageNavigation.navigateToDashboard(context, replace: true);
        break;
    }

    // Show a snackbar to indicate the navigation
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'นำทางไปยัง ${MainPageNavigation.getTabName(_getTabIndexForCategory(notification.category))}',
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.textPrimary,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Helper method to get tab index for notification category
  int _getTabIndexForCategory(String category) {
    switch (category) {
      case 'โครงการใหม่':
      case 'การจับคู่':
        return MainPageNavigation.matchingTab;
      case 'สถานะโครงการ':
      case 'การติดตาม':
        return MainPageNavigation.trackingTab;
      case 'ข้อมูลโปรไฟล์':
      case 'การตั้งค่า':
        return MainPageNavigation.profileTab;
      case 'ผลการค้นหา':
        return MainPageNavigation.searchTab;
      default:
        return MainPageNavigation.dashboardTab;
    }
  }

  void _navigateToNewsDetail(notification_entity.Notification notification) {
    // Create a news object from notification data
    final news = News(
      id: notification.id,
      title: notification.title,
      content: _getNewsContentFromNotification(notification),
      category: notification.category,
      publishedDate: notification.date,
      downloadUrl: 'https://example.com/news-document.pdf',
      author: 'ศูนย์ข้อมูลและเทคโนโลยีสารสนเทศ',
    );

    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => NewsDetailPage(news: news)));
  }

  String _getNewsContentFromNotification(
    notification_entity.Notification notification,
  ) {
    // In a real app, this would fetch the full content from an API
    // For now, return mock content based on the notification title
    if (notification.title.contains('รายงานผลการให้บริการ')) {
      return '''ศูนย์ข้อมูลและเทคโนโลยีสารสนเทศ (ศทส.) ขอรายงานผลการให้บริการของศูนย์ข้อมูลที่ปรึกษา (ศท.)

ณ วันที่ 31 ธันวาคม 2567 ดังนี้

1. ภาพรวมสถานะการขึ้นทะเบียนที่ปรึกษาณ วันที่ 31 ธันวาคม 2567 มีจำนวนที่ปรึกษา
ที่ขึ้นทะเบียนกับ ศท. รวมทั้งสิ้น 3,195 ราย ประกอบด้วยที่ปรึกษาอิสระ 602 ราย และที่ปรึกษา
นิติบุคคล 2,593 ราย โดยมีรายได้ค่าธรรมเนียมการขึ้นทะเบียนที่ปรึกษา รวมทั้งสิ้น 29,660,000 บาท

2. การให้บริการของ ศท. ประจำไตรมาสที่ 1 (วันที่ 1 ตุลาคม 2567 จนถึงวันที่ 31 ธันวาคม 2567) สรุปได้ดังนี้

   2.1 จำนวนที่ปรึกษาที่ขอรับบริการ รวมทั้งสิ้น 235 รายแบ่งเป็นที่ปรึกษาที่ขึ้นทะเบียนที่ปรึกษา
จำนวน 70 ราย ที่ปรึกษาที่เพิ่มเติมผลงานที่ปรึกษาจำนวน 50 ราย ที่ปรึกษาที่เปลี่ยนแปลงข้อมูลที่ปรึกษา จำนวน 33 ราย
และที่ปรึกษาที่รายงานข้อมูลสถานะการขึ้นทะเบียนที่ปรึกษา จำนวน 82 ราย

   2.2 จำนวนการขอรายชื่อที่ปรึกษาจากหน่วยงานของรัฐ รวมทั้งสิ้น 5 ราย และจำนวน การดาวน์โหลด
รายงานการค้นหารายชื่อที่ปรึกษาจากเว็บไซต์ของ ศท. (www.consultant.pdmo.go.th) รวมทั้งสิ้น 1,661 ครั้ง

   2.3 รายได้ค่าธรรมเนียมการขึ้นทะเบียนที่ปรึกษา รวมทั้งสิ้น 615,000 บาท''';
    }

    return 'เนื้อหาข่าวประกาศ: ${notification.title}';
  }

  // Selection mode methods
  void _enterSelectionMode(notification_entity.Notification notification) {
    setState(() {
      _isSelectionMode = true;
      _selectedNotificationIds.add(notification.id);
    });
  }

  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedNotificationIds.clear();
    });
  }

  void _handleSelectionChanged(String notificationId, bool isSelected) {
    setState(() {
      if (isSelected) {
        _selectedNotificationIds.add(notificationId);
      } else {
        _selectedNotificationIds.remove(notificationId);
      }

      // Exit selection mode if no items are selected
      if (_selectedNotificationIds.isEmpty) {
        _isSelectionMode = false;
      }
    });
  }

  Future<void> _showDeleteConfirmation() async {
    final confirmed = await DialogConfirm.show(
      context: context,
      title: 'ลบรายการ',
      message: 'คุณต้องการลบรายการที่เลือก ใช่หรือไม่',
    );

    if (confirmed == true) {
      await _deleteSelectedNotifications();
    }
  }

  Future<void> _deleteSelectedNotifications() async {
    // Remove selected notifications from the list
    setState(() {
      _allNotifications.removeWhere(
        (notification) => _selectedNotificationIds.contains(notification.id),
      );
      _groupNotificationsByDate();
      _isSelectionMode = false;
      _selectedNotificationIds.clear();
    });

    // Show success dialog
    await DialogSuccess.show(context: context, title: 'ลบสำเร็จ');
  }
}
