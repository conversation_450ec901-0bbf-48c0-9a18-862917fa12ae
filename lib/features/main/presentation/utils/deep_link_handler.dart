import 'package:flutter/material.dart';
import 'package:mcdc/features/main/presentation/utils/main_page_navigation.dart';

/// Deep link handler that demonstrates the dynamic routing functionality
/// This class shows how to handle deep links and navigate to specific tabs
class DeepLinkHandler {
  /// Handle deep link URLs and navigate to appropriate tabs
  /// 
  /// Supported URL patterns:
  /// - /main/dashboard or /main/home -> Dashboard tab
  /// - /main/tracking -> Tracking tab  
  /// - /main/matching -> Matching tab
  /// - /main/search -> Search tab
  /// - /main/profile -> Profile tab
  /// - /main?tabIndex=N -> Tab by index (0-4)
  static Future<void> handleDeepLink(
    BuildContext context,
    String url, {
    bool replace = true,
  }) async {
    final uri = Uri.tryParse(url);
    if (uri == null) return;

    // Handle query parameter navigation
    if (uri.path == '/main' && uri.queryParameters.containsKey('tabIndex')) {
      final tabIndexStr = uri.queryParameters['tabIndex'];
      final tabIndex = int.tryParse(tabIndexStr ?? '');
      if (tabIndex != null) {
        await MainPageNavigation.navigateToTab(context, tabIndex, replace: replace);
        return;
      }
    }

    // Handle path-based navigation
    switch (uri.path) {
      case '/main/dashboard':
      case '/main/home':
        await MainPageNavigation.navigateToDashboard(context, replace: replace);
        break;
      case '/main/tracking':
        await MainPageNavigation.navigateToTracking(context, replace: replace);
        break;
      case '/main/matching':
        await MainPageNavigation.navigateToMatching(context, replace: replace);
        break;
      case '/main/search':
        await MainPageNavigation.navigateToSearch(context, replace: replace);
        break;
      case '/main/profile':
        await MainPageNavigation.navigateToProfile(context, replace: replace);
        break;
      case '/main':
        // Default to dashboard if no specific tab is specified
        await MainPageNavigation.navigateToDashboard(context, replace: replace);
        break;
      default:
        // Unknown deep link, navigate to dashboard
        await MainPageNavigation.navigateToDashboard(context, replace: replace);
        break;
    }
  }

  /// Handle notification-based deep links
  /// This method demonstrates how to handle deep links from push notifications
  static Future<void> handleNotificationDeepLink(
    BuildContext context,
    Map<String, dynamic> notificationData, {
    bool replace = true,
  }) async {
    final targetTab = notificationData['targetTab'] as String?;
    final tabIndex = notificationData['tabIndex'] as int?;

    if (tabIndex != null) {
      await MainPageNavigation.navigateToTab(context, tabIndex, replace: replace);
      return;
    }

    switch (targetTab) {
      case 'dashboard':
      case 'home':
        await MainPageNavigation.navigateToDashboard(context, replace: replace);
        break;
      case 'tracking':
        await MainPageNavigation.navigateToTracking(context, replace: replace);
        break;
      case 'matching':
        await MainPageNavigation.navigateToMatching(context, replace: replace);
        break;
      case 'search':
        await MainPageNavigation.navigateToSearch(context, replace: replace);
        break;
      case 'profile':
        await MainPageNavigation.navigateToProfile(context, replace: replace);
        break;
      default:
        await MainPageNavigation.navigateToDashboard(context, replace: replace);
        break;
    }
  }

  /// Get the appropriate tab index for a given deep link path
  static int getTabIndexForPath(String path) {
    switch (path) {
      case '/main/dashboard':
      case '/main/home':
        return MainPageNavigation.dashboardTab;
      case '/main/tracking':
        return MainPageNavigation.trackingTab;
      case '/main/matching':
        return MainPageNavigation.matchingTab;
      case '/main/search':
        return MainPageNavigation.searchTab;
      case '/main/profile':
        return MainPageNavigation.profileTab;
      default:
        return MainPageNavigation.dashboardTab;
    }
  }

  /// Validate if a deep link URL is supported
  static bool isValidDeepLink(String url) {
    final uri = Uri.tryParse(url);
    if (uri == null) return false;

    // Check for query parameter format
    if (uri.path == '/main' && uri.queryParameters.containsKey('tabIndex')) {
      final tabIndexStr = uri.queryParameters['tabIndex'];
      final tabIndex = int.tryParse(tabIndexStr ?? '');
      return tabIndex != null && tabIndex >= 0 && tabIndex <= 4;
    }

    // Check for path-based format
    const validPaths = [
      '/main',
      '/main/dashboard',
      '/main/home',
      '/main/tracking',
      '/main/matching',
      '/main/search',
      '/main/profile',
    ];

    return validPaths.contains(uri.path);
  }

  /// Generate a deep link URL for a specific tab
  static String generateDeepLink(int tabIndex) {
    if (tabIndex < 0 || tabIndex > 4) {
      tabIndex = 0; // Default to dashboard
    }
    return '/main?tabIndex=$tabIndex';
  }

  /// Generate a path-based deep link URL for a specific tab
  static String generatePathDeepLink(int tabIndex) {
    switch (tabIndex) {
      case MainPageNavigation.dashboardTab:
        return '/main/dashboard';
      case MainPageNavigation.trackingTab:
        return '/main/tracking';
      case MainPageNavigation.matchingTab:
        return '/main/matching';
      case MainPageNavigation.searchTab:
        return '/main/search';
      case MainPageNavigation.profileTab:
        return '/main/profile';
      default:
        return '/main/dashboard';
    }
  }
}
