// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'validation_errors_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ValidationErrorsModel {

@JsonKey(name: 'validation_errors') Map<String, List<String>> get validationErrors;
/// Create a copy of ValidationErrorsModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ValidationErrorsModelCopyWith<ValidationErrorsModel> get copyWith => _$ValidationErrorsModelCopyWithImpl<ValidationErrorsModel>(this as ValidationErrorsModel, _$identity);

  /// Serializes this ValidationErrorsModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ValidationErrorsModel&&const DeepCollectionEquality().equals(other.validationErrors, validationErrors));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(validationErrors));

@override
String toString() {
  return 'ValidationErrorsModel(validationErrors: $validationErrors)';
}


}

/// @nodoc
abstract mixin class $ValidationErrorsModelCopyWith<$Res>  {
  factory $ValidationErrorsModelCopyWith(ValidationErrorsModel value, $Res Function(ValidationErrorsModel) _then) = _$ValidationErrorsModelCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'validation_errors') Map<String, List<String>> validationErrors
});




}
/// @nodoc
class _$ValidationErrorsModelCopyWithImpl<$Res>
    implements $ValidationErrorsModelCopyWith<$Res> {
  _$ValidationErrorsModelCopyWithImpl(this._self, this._then);

  final ValidationErrorsModel _self;
  final $Res Function(ValidationErrorsModel) _then;

/// Create a copy of ValidationErrorsModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? validationErrors = null,}) {
  return _then(_self.copyWith(
validationErrors: null == validationErrors ? _self.validationErrors : validationErrors // ignore: cast_nullable_to_non_nullable
as Map<String, List<String>>,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ValidationErrorsModel implements ValidationErrorsModel {
  const _ValidationErrorsModel({@JsonKey(name: 'validation_errors') required final  Map<String, List<String>> validationErrors}): _validationErrors = validationErrors;
  factory _ValidationErrorsModel.fromJson(Map<String, dynamic> json) => _$ValidationErrorsModelFromJson(json);

 final  Map<String, List<String>> _validationErrors;
@override@JsonKey(name: 'validation_errors') Map<String, List<String>> get validationErrors {
  if (_validationErrors is EqualUnmodifiableMapView) return _validationErrors;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_validationErrors);
}


/// Create a copy of ValidationErrorsModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ValidationErrorsModelCopyWith<_ValidationErrorsModel> get copyWith => __$ValidationErrorsModelCopyWithImpl<_ValidationErrorsModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ValidationErrorsModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ValidationErrorsModel&&const DeepCollectionEquality().equals(other._validationErrors, _validationErrors));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_validationErrors));

@override
String toString() {
  return 'ValidationErrorsModel(validationErrors: $validationErrors)';
}


}

/// @nodoc
abstract mixin class _$ValidationErrorsModelCopyWith<$Res> implements $ValidationErrorsModelCopyWith<$Res> {
  factory _$ValidationErrorsModelCopyWith(_ValidationErrorsModel value, $Res Function(_ValidationErrorsModel) _then) = __$ValidationErrorsModelCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'validation_errors') Map<String, List<String>> validationErrors
});




}
/// @nodoc
class __$ValidationErrorsModelCopyWithImpl<$Res>
    implements _$ValidationErrorsModelCopyWith<$Res> {
  __$ValidationErrorsModelCopyWithImpl(this._self, this._then);

  final _ValidationErrorsModel _self;
  final $Res Function(_ValidationErrorsModel) _then;

/// Create a copy of ValidationErrorsModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? validationErrors = null,}) {
  return _then(_ValidationErrorsModel(
validationErrors: null == validationErrors ? _self._validationErrors : validationErrors // ignore: cast_nullable_to_non_nullable
as Map<String, List<String>>,
  ));
}


}

// dart format on
