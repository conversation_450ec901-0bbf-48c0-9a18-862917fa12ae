import 'package:freezed_annotation/freezed_annotation.dart';

part 'validation_errors_model.freezed.dart';
part 'validation_errors_model.g.dart';

@freezed
abstract class ValidationErrorsModel with _$ValidationErrorsModel {
  const factory ValidationErrorsModel({
    @JsonKey(name: 'validation_errors')
    required Map<String, List<String>> validationErrors,
  }) = _ValidationErrorsModel;

  factory ValidationErrorsModel.fromJson(Map<String, dynamic> json) =>
      _$ValidationErrorsModelFromJson(json);
}
