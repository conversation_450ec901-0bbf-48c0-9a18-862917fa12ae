class ServerException implements Exception {
  final String? code;
  final String? message;

  ServerException({this.code, this.message});
}

class ValidationException implements Exception {
  final String? message;
  final String? code;

  ValidationException({this.message, this.code});
}

class CacheException implements Exception {}

class UnhandledException implements Exception {}

class NullResponseException implements Exception {}

class UnderageException implements Exception {}
