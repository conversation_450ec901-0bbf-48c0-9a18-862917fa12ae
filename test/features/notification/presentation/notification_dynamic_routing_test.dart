import 'package:flutter_test/flutter_test.dart';
import 'package:mcdc/features/main/presentation/utils/main_page_navigation.dart';

void main() {
  group('Notification Dynamic Routing', () {
    test('should map notification categories to correct tab indices', () {
      // Test mapping for project-related notifications
      expect(_getTabIndexForCategory('โครงการใหม่'), MainPageNavigation.matchingTab);
      expect(_getTabIndexForCategory('การจับคู่'), MainPageNavigation.matchingTab);
      
      // Test mapping for tracking-related notifications
      expect(_getTabIndexForCategory('สถานะโครงการ'), MainPageNavigation.trackingTab);
      expect(_getTabIndexForCategory('การติดตาม'), MainPageNavigation.trackingTab);
      
      // Test mapping for profile-related notifications
      expect(_getTabIndexForCategory('ข้อมูลโปรไฟล์'), MainPageNavigation.profileTab);
      expect(_getTabIndexForCategory('การตั้งค่า'), MainPageNavigation.profileTab);
      
      // Test mapping for search-related notifications
      expect(_getTabIndexForCategory('ผลการค้นหา'), MainPageNavigation.searchTab);
      
      // Test default mapping
      expect(_getTabIndexForCategory('unknown'), MainPageNavigation.dashboardTab);
      expect(_getTabIndexForCategory(''), MainPageNavigation.dashboardTab);
    });

    test('should return correct tab names for notification categories', () {
      expect(MainPageNavigation.getTabName(_getTabIndexForCategory('โครงการใหม่')), 'Matching');
      expect(MainPageNavigation.getTabName(_getTabIndexForCategory('สถานะโครงการ')), 'Tracking');
      expect(MainPageNavigation.getTabName(_getTabIndexForCategory('ข้อมูลโปรไฟล์')), 'Profile');
      expect(MainPageNavigation.getTabName(_getTabIndexForCategory('ผลการค้นหา')), 'Search');
      expect(MainPageNavigation.getTabName(_getTabIndexForCategory('unknown')), 'Dashboard');
    });
  });
}

/// Helper method to get tab index for notification category
/// This mirrors the implementation in notification_page.dart
int _getTabIndexForCategory(String category) {
  switch (category) {
    case 'โครงการใหม่':
    case 'การจับคู่':
      return MainPageNavigation.matchingTab;
    case 'สถานะโครงการ':
    case 'การติดตาม':
      return MainPageNavigation.trackingTab;
    case 'ข้อมูลโปรไฟล์':
    case 'การตั้งค่า':
      return MainPageNavigation.profileTab;
    case 'ผลการค้นหา':
      return MainPageNavigation.searchTab;
    default:
      return MainPageNavigation.dashboardTab;
  }
}
