import 'package:flutter_test/flutter_test.dart';
import 'package:mcdc/features/main/presentation/utils/deep_link_handler.dart';
import 'package:mcdc/features/main/presentation/utils/main_page_navigation.dart';

void main() {
  group('DeepLinkHandler', () {
    group('getTabIndexForPath', () {
      test('should return correct tab indices for valid paths', () {
        expect(DeepLinkHandler.getTabIndexForPath('/main/dashboard'), MainPageNavigation.dashboardTab);
        expect(DeepLinkHandler.getTabIndexForPath('/main/home'), MainPageNavigation.dashboardTab);
        expect(DeepLinkHandler.getTabIndexForPath('/main/tracking'), MainPageNavigation.trackingTab);
        expect(DeepLinkHandler.getTabIndexForPath('/main/matching'), MainPageNavigation.matchingTab);
        expect(DeepLinkHandler.getTabIndexForPath('/main/search'), MainPageNavigation.searchTab);
        expect(DeepLinkHandler.getTabIndexForPath('/main/profile'), MainPageNavigation.profileTab);
      });

      test('should return dashboard tab for invalid paths', () {
        expect(DeepLinkHandler.getTabIndexForPath('/invalid'), MainPageNavigation.dashboardTab);
        expect(DeepLinkHandler.getTabIndexForPath(''), MainPageNavigation.dashboardTab);
        expect(DeepLinkHandler.getTabIndexForPath('/main/unknown'), MainPageNavigation.dashboardTab);
      });
    });

    group('isValidDeepLink', () {
      test('should validate query parameter format correctly', () {
        expect(DeepLinkHandler.isValidDeepLink('/main?tabIndex=0'), true);
        expect(DeepLinkHandler.isValidDeepLink('/main?tabIndex=1'), true);
        expect(DeepLinkHandler.isValidDeepLink('/main?tabIndex=4'), true);
        expect(DeepLinkHandler.isValidDeepLink('/main?tabIndex=5'), false);
        expect(DeepLinkHandler.isValidDeepLink('/main?tabIndex=-1'), false);
        expect(DeepLinkHandler.isValidDeepLink('/main?tabIndex=abc'), false);
      });

      test('should validate path format correctly', () {
        expect(DeepLinkHandler.isValidDeepLink('/main'), true);
        expect(DeepLinkHandler.isValidDeepLink('/main/dashboard'), true);
        expect(DeepLinkHandler.isValidDeepLink('/main/home'), true);
        expect(DeepLinkHandler.isValidDeepLink('/main/tracking'), true);
        expect(DeepLinkHandler.isValidDeepLink('/main/matching'), true);
        expect(DeepLinkHandler.isValidDeepLink('/main/search'), true);
        expect(DeepLinkHandler.isValidDeepLink('/main/profile'), true);
        expect(DeepLinkHandler.isValidDeepLink('/main/unknown'), false);
        expect(DeepLinkHandler.isValidDeepLink('/invalid'), false);
      });

      test('should handle invalid URLs', () {
        expect(DeepLinkHandler.isValidDeepLink(''), false);
        expect(DeepLinkHandler.isValidDeepLink('invalid-url'), false);
      });
    });

    group('generateDeepLink', () {
      test('should generate correct query parameter URLs', () {
        expect(DeepLinkHandler.generateDeepLink(0), '/main?tabIndex=0');
        expect(DeepLinkHandler.generateDeepLink(1), '/main?tabIndex=1');
        expect(DeepLinkHandler.generateDeepLink(4), '/main?tabIndex=4');
      });

      test('should default to dashboard for invalid indices', () {
        expect(DeepLinkHandler.generateDeepLink(-1), '/main?tabIndex=0');
        expect(DeepLinkHandler.generateDeepLink(5), '/main?tabIndex=0');
        expect(DeepLinkHandler.generateDeepLink(100), '/main?tabIndex=0');
      });
    });

    group('generatePathDeepLink', () {
      test('should generate correct path URLs', () {
        expect(DeepLinkHandler.generatePathDeepLink(MainPageNavigation.dashboardTab), '/main/dashboard');
        expect(DeepLinkHandler.generatePathDeepLink(MainPageNavigation.trackingTab), '/main/tracking');
        expect(DeepLinkHandler.generatePathDeepLink(MainPageNavigation.matchingTab), '/main/matching');
        expect(DeepLinkHandler.generatePathDeepLink(MainPageNavigation.searchTab), '/main/search');
        expect(DeepLinkHandler.generatePathDeepLink(MainPageNavigation.profileTab), '/main/profile');
      });

      test('should default to dashboard for invalid indices', () {
        expect(DeepLinkHandler.generatePathDeepLink(-1), '/main/dashboard');
        expect(DeepLinkHandler.generatePathDeepLink(5), '/main/dashboard');
        expect(DeepLinkHandler.generatePathDeepLink(100), '/main/dashboard');
      });
    });
  });
}
